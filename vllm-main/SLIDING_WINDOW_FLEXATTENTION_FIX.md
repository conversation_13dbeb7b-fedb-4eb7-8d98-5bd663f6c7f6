# FlexAttention Sliding Window Support Implementation

## Summary

This PR implements sliding window attention support for the FlexAttention backend in vLLM, resolving issue #24358 where models like `unsloth/gpt-oss-20b-unsloth-bnb-4bit` would fail with `NotImplementedError: FlexAttention does not support sliding window yet.`

## Problem

The FlexAttention backend was missing sliding window attention support, causing failures when:
1. Models with sliding window attention (like GptOss) were loaded
2. Hardware limitations forced fallback to FlexAttention (e.g., RTX 2080 with compute capability < 8.0)
3. FlashAttention v2 was unavailable

## Solution

### 1. Added Sliding Window Mask Function

```python
def sliding_window_causal_mask_mod(window_size: int):
    """Create a sliding window causal mask function."""
    def mask_mod(b: torch.Tensor, h: torch.Tensor, q_idx: torch.Tensor,
                 kv_idx: torch.Tensor):
        # Causal mask: can only attend to current and previous tokens
        causal_mask = q_idx >= kv_idx
        # Sliding window mask: can only attend to tokens within the window
        window_mask = q_idx - kv_idx < window_size
        return causal_mask & window_mask
    
    return mask_mod
```

### 2. Updated FlexAttentionImpl

- Removed the `NotImplementedError` for sliding window
- Added proper sliding window tuple configuration: `(sliding_window - 1, 0)`
- Integrated sliding window mask selection logic

### 3. Enhanced FlexAttentionMetadata

- Added `sliding_window: Optional[int]` field
- Updated metadata builder to pass sliding window information
- Added logic to select appropriate mask function based on sliding window presence

### 4. Updated FlexAttentionMetadataBuilder

- Added sliding window extraction from model configuration
- Updated metadata creation to include sliding window parameter
- Added mask function selection logic

## Key Changes

### Files Modified:
- `vllm/v1/attention/backends/flex_attention.py`: Core implementation
- `tests/v1/attention/backends/test_flex_attention_sliding_window.py`: Comprehensive tests
- `tests/models/test_gpt_oss_sliding_window.py`: Integration tests

### Implementation Details:

1. **Sliding Window Logic**: Implements causal attention with a sliding window constraint
2. **Backward Compatibility**: Models without sliding window continue to work unchanged
3. **Proper Integration**: Sliding window information is extracted from model config and passed through the metadata pipeline
4. **Comprehensive Testing**: Unit tests for mask functions and integration tests for GptOss models

## Testing

### Unit Tests
- `test_sliding_window_mask_function()`: Tests the sliding window mask logic
- `test_causal_mask_function()`: Ensures standard causal mask still works
- `test_flex_attention_impl_with_sliding_window()`: Tests FlexAttentionImpl initialization
- `test_flex_attention_metadata_*()`: Tests metadata handling

### Integration Tests
- `test_oai_attention_initialization()`: Tests GptOss model initialization
- `test_sliding_window_per_layer()`: Tests per-layer sliding window application
- `test_attention_layer_with_sliding_window()`: Tests full attention layer

### Edge Cases
- Window size of 1
- Large window sizes
- Models without sliding window
- Different layer configurations

## Validation

The fix resolves the original issue:
```bash
# This should now work instead of raising NotImplementedError
vllm serve unsloth/gpt-oss-20b-unsloth-bnb-4bit
```

## Performance Considerations

- Sliding window mask computation is efficient using tensor operations
- No performance impact on models without sliding window
- Memory usage remains the same as the mask is computed on-the-fly

## Backward Compatibility

- ✅ Models without sliding window continue to work unchanged
- ✅ Existing FlexAttention functionality is preserved
- ✅ No breaking changes to public APIs

## Future Improvements

1. **Optimization**: Could cache mask computations for repeated patterns
2. **Extended Support**: Could add support for different sliding window patterns
3. **Hardware Optimization**: Could add GPU-specific optimizations for sliding window

## Related Issues

- Fixes #24358: FlexAttention does not support sliding window yet
- Enables GptOss models to work with FlexAttention backend
- Improves compatibility with older GPUs that can't use FlashAttention v2
