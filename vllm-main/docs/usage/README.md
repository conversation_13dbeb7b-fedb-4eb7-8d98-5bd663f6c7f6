# Using vLLM

First, vLLM must be [installed](../getting_started/installation) for your chosen device in either a Python or Docker environment.

Then, vLLM supports the following usage patterns:

- [Inference and Serving](../serving/offline_inference.md): Run a single instance of a model.
- [Deployment](../deployment/docker.md): Scale up model instances for production.
- [Training](../training/rlhf.md): Train or fine-tune a model.
