# SPDX-License-Identifier: Apache-2.0
# SPDX-FileCopyrightText: Copyright contributors to the vLLM project

"""
Tests for DeepSeek-R1 low batch size optimizations.
"""

import pytest
import torch
import time
from unittest.mock import Mock, patch
from typing import Dict, Any

from vllm.config.low_batch_optimization import (
    LowBatchOptimizationConfig,
    initialize_low_batch_optimization_config
)
from vllm.distributed.low_batch_optimizer import (
    LowBatchSizeOptimizer,
    initialize_low_batch_optimizer
)
from vllm.distributed.smart_expert_scheduler import (
    SmartExpertScheduler,
    initialize_smart_expert_scheduler
)
from vllm.model_executor.layers.memory_optimizer import (
    MemoryAccessOptimizer,
    initialize_memory_optimizer
)
from vllm.optimization.deepseek_low_batch_optimizer import (
    DeepSeekLowBatchOptimizer
)


class TestLowBatchOptimizationConfig:
    """Test low batch optimization configuration."""
    
    def test_default_config(self):
        """Test default configuration values."""
        config = LowBatchOptimizationConfig()
        assert config.enable_low_batch_optimization is True
        assert config.enable_single_token_optimization is True
        assert config.single_token_threshold == 1
        assert config.small_batch_threshold == 4
    
    def test_config_validation(self):
        """Test configuration validation."""
        # Test invalid expert cache size
        with pytest.raises(ValueError):
            LowBatchOptimizationConfig(expert_cache_size=-1)
        
        # Test invalid thresholds
        with pytest.raises(ValueError):
            LowBatchOptimizationConfig(
                single_token_threshold=2,
                small_batch_threshold=1
            )
    
    def test_batch_size_decisions(self):
        """Test batch size optimization decisions."""
        config = LowBatchOptimizationConfig()
        
        # Single token optimization
        assert config.should_use_single_token_optimization(1) is True
        assert config.should_use_single_token_optimization(2) is False
        
        # Small batch optimization
        assert config.should_optimize_for_batch_size(1) is True
        assert config.should_optimize_for_batch_size(4) is True
        assert config.should_optimize_for_batch_size(8) is False


class TestLowBatchSizeOptimizer:
    """Test low batch size optimizer."""
    
    def setup_method(self):
        """Setup test environment."""
        self.optimizer = LowBatchSizeOptimizer()
    
    def test_synchronization_skip_decision(self):
        """Test synchronization skip decisions."""
        # Should skip for single token with one active rank
        assert self.optimizer.should_skip_synchronization(1, 1) is True
        
        # Should not skip for larger batches
        assert self.optimizer.should_skip_synchronization(8, 1) is False
        
        # Should not skip with multiple active ranks
        assert self.optimizer.should_skip_synchronization(1, 4) is False
    
    def test_expert_routing_cache(self):
        """Test expert routing cache functionality."""
        # Test cache miss
        cached_routing = self.optimizer.get_cached_expert_routing(12345)
        assert cached_routing is None
        
        # Test cache hit
        test_routing = torch.tensor([1, 2, 3])
        self.optimizer.cache_expert_routing(12345, test_routing)
        cached_routing = self.optimizer.get_cached_expert_routing(12345)
        assert cached_routing is not None
        assert torch.equal(cached_routing, test_routing)
    
    def test_performance_stats(self):
        """Test performance statistics tracking."""
        # Record some decode times
        self.optimizer.record_decode_time(10.5)
        self.optimizer.record_decode_time(8.2)
        
        stats = self.optimizer.get_performance_stats()
        assert 'avg_decode_time' in stats
        assert stats['avg_decode_time'] > 0
        assert stats['num_decode_samples'] == 2


class TestSmartExpertScheduler:
    """Test smart expert scheduler."""
    
    def setup_method(self):
        """Setup test environment."""
        self.scheduler = SmartExpertScheduler(
            num_experts=256,
            dp_size=8,
            ep_size=4
        )
    
    def test_dummy_forward_elimination(self):
        """Test dummy forward elimination decisions."""
        # For single token with few active ranks, should skip dummy forwards
        skip_decisions = self.scheduler.should_eliminate_dummy_forwards(
            batch_size=1,
            active_ranks=[0, 1]
        )
        
        # Should have decisions for all ranks
        assert len(skip_decisions) == 8
        
        # Some ranks should be able to skip
        skipped_ranks = sum(1 for skip in skip_decisions.values() if skip)
        assert skipped_ranks > 0
    
    def test_communication_pattern_optimization(self):
        """Test communication pattern optimization."""
        # Single token should use point-to-point
        pattern = self.scheduler.optimize_communication_pattern(
            batch_size=1,
            expert_distribution={0: [1, 2]}
        )
        assert pattern in ['point_to_point', 'async_all_to_all']
        
        # Larger batches should use standard patterns
        pattern = self.scheduler.optimize_communication_pattern(
            batch_size=16,
            expert_distribution={0: [1, 2, 3, 4]}
        )
        assert pattern == 'standard_all_to_all'
    
    def test_routing_cache(self):
        """Test expert routing cache."""
        # Test cache miss
        cached = self.scheduler.get_cached_routing(12345)
        assert cached is None
        
        # Test cache hit
        topk_weights = torch.tensor([0.8, 0.2])
        topk_ids = torch.tensor([1, 5])
        self.scheduler.cache_routing(12345, topk_weights, topk_ids)
        
        cached = self.scheduler.get_cached_routing(12345)
        assert cached is not None
        assert torch.equal(cached[0], topk_weights)
        assert torch.equal(cached[1], topk_ids)


class TestMemoryAccessOptimizer:
    """Test memory access optimizer."""
    
    def setup_method(self):
        """Setup test environment."""
        self.optimizer = MemoryAccessOptimizer()
    
    def test_expert_weight_prefetching(self):
        """Test expert weight prefetching decisions."""
        # Should prefetch for small batches
        prefetch_list = self.optimizer.should_prefetch_expert_weights([1, 2, 3], 1)
        assert len(prefetch_list) > 0
        
        # Should not prefetch for large batches
        prefetch_list = self.optimizer.should_prefetch_expert_weights([1, 2, 3], 16)
        assert len(prefetch_list) == 0
    
    def test_tensor_layout_optimization(self):
        """Test tensor layout optimization."""
        # Create test tensor
        test_tensor = torch.randn(1, 4096)
        
        # Optimize for single token
        optimized = self.optimizer.optimize_tensor_layout_for_small_batch(test_tensor, 1)
        assert optimized.is_contiguous()
    
    def test_expert_weight_caching(self):
        """Test expert weight caching."""
        # Test caching
        weights = {'w1': torch.randn(100, 100), 'w2': torch.randn(100, 100)}
        success = self.optimizer.cache_expert_weights(1, weights)
        assert success is True
        
        # Test retrieval
        cached_weights = self.optimizer.get_cached_expert_weights(1)
        assert cached_weights is not None
        assert 'w1' in cached_weights
        assert 'w2' in cached_weights


class TestDeepSeekLowBatchOptimizer:
    """Test the main DeepSeek optimizer integration."""
    
    def setup_method(self):
        """Setup test environment."""
        # Mock parallel config
        self.parallel_config = Mock()
        self.parallel_config.data_parallel_size = 8
        self.parallel_config.expert_parallel_size = 4
        
        # Initialize optimizer
        self.optimizer = DeepSeekLowBatchOptimizer(
            model_name="deepseek-ai/DeepSeek-V3-0324",
            parallel_config=self.parallel_config
        )
    
    def test_optimizer_initialization(self):
        """Test optimizer initialization."""
        assert self.optimizer.enabled is True
        assert self.optimizer.model_name == "deepseek-ai/DeepSeek-V3-0324"
    
    def test_forward_pass_optimization(self):
        """Test forward pass optimization."""
        # Create test inputs
        hidden_states = torch.randn(1, 4096)
        expert_ids = [1, 5, 10]
        
        # Apply optimizations
        result = self.optimizer.optimize_forward_pass(
            batch_size=1,
            hidden_states=hidden_states,
            expert_ids=expert_ids,
            request_id="test_request_1"
        )
        
        # Check results
        assert 'optimizations_applied' in result
        assert 'metadata' in result
        assert 'batch_size' in result
        assert result['batch_size'] == 1
        
        # Should have applied some optimizations for batch size 1
        assert len(result['optimizations_applied']) > 0
    
    def test_single_token_optimizations(self):
        """Test single token specific optimizations."""
        hidden_states = torch.randn(1, 4096)
        
        result = self.optimizer.optimize_forward_pass(
            batch_size=1,
            hidden_states=hidden_states,
            expert_ids=[1, 2],
            request_id="single_token_test"
        )
        
        # Should apply single token optimizations
        optimizations = result['optimizations_applied']
        assert any('single_token' in opt or 'memory_layout' in opt 
                  for opt in optimizations)
    
    def test_performance_summary(self):
        """Test performance summary generation."""
        summary = self.optimizer.get_performance_summary()
        
        assert 'optimizations_enabled' in summary
        assert summary['optimizations_enabled'] is True
        assert 'model_name' in summary
        assert 'config' in summary
        assert 'performance' in summary


@pytest.mark.integration
class TestIntegrationOptimizations:
    """Integration tests for the complete optimization pipeline."""
    
    def test_end_to_end_optimization(self):
        """Test end-to-end optimization pipeline."""
        # Initialize all components
        initialize_low_batch_optimization_config(
            enable_low_batch_optimization=True,
            enable_single_token_optimization=True
        )
        
        initialize_low_batch_optimizer(
            enable_single_token_optimization=True
        )
        
        initialize_memory_optimizer(
            enable_prefetching=True,
            cache_size_mb=32
        )
        
        # Create optimizer
        parallel_config = Mock()
        parallel_config.data_parallel_size = 4
        parallel_config.expert_parallel_size = 2
        
        optimizer = DeepSeekLowBatchOptimizer(
            model_name="deepseek-ai/DeepSeek-V3-0324",
            parallel_config=parallel_config
        )
        
        # Test multiple requests
        for i in range(10):
            hidden_states = torch.randn(1, 4096)
            result = optimizer.optimize_forward_pass(
                batch_size=1,
                hidden_states=hidden_states,
                expert_ids=[i % 8, (i + 1) % 8],
                request_id=f"request_{i}"
            )
            
            # Finalize request
            optimizer.finalize_request(
                request_id=f"request_{i}",
                batch_size=1,
                num_tokens=1,
                optimization_stats=result['metadata']
            )
        
        # Check performance summary
        summary = optimizer.get_performance_summary()
        assert summary['optimizations_enabled'] is True
        assert 'performance' in summary


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
