name: PR Reminder Comment Bot
permissions:
  pull-requests: write
on:
  pull_request_target:
    types: [opened]
jobs:
  pr_reminder:
    runs-on: ubuntu-latest
    steps:
      - name: Remind to run full CI on PR
        uses: actions/github-script@60a0d83039c74a4aee543508d2ffcb1c3799cdea # v7.0.1
        with:
          script: |
            try {
              // Get the PR author
              const prAuthor = context.payload.pull_request.user.login;
              
              // Check if this is the author's first PR in this repository
              // Use GitHub's search API to find all PRs by this author
              const { data: searchResults } = await github.rest.search.issuesAndPullRequests({
                q: `repo:${context.repo.owner}/${context.repo.repo} type:pr author:${prAuthor}`,
                per_page: 100  
              });
              
              const authorPRCount = searchResults.total_count;
              
              console.log(`Found ${authorPRCount} PRs by ${prAuthor}`);
              
              // Only post comment if this is the first PR (only one PR by this author)
              if (authorPRCount === 1) {
                console.log(`Posting welcome comment for first-time contributor: ${prAuthor}`);
                await github.rest.issues.createComment({
                owner: context.repo.owner,
                repo: context.repo.repo,
                issue_number: context.issue.number,
                body: '👋 Hi! Thank you for contributing to the vLLM project.\n\n' +
                  '💬 Join our developer Slack at https://slack.vllm.ai to discuss your PR in #pr-reviews, coordinate on features in #feat- channels, or join special interest groups in #sig- channels.\n\n' +
                  'Just a reminder: PRs would not trigger full CI run by default. Instead, it would only run `fastcheck` CI which starts running only a small and essential subset of CI tests to quickly catch errors. \n\n' +
                  'You ask your reviewers to trigger select CI tests on top of `fastcheck` CI. \n\n' +
                  'Once the PR is approved and ready to go, your PR reviewer(s) can run CI to test the changes comprehensively before merging.\n\n' +
                  'To run CI, PR reviewers can either: Add `ready` label to the PR or enable auto-merge.\n\n' +
                  'If you have any questions, please reach out to us on Slack at https://slack.vllm.ai.\n\n' +
                  '🚀'
                });
              } else {
                console.log(`Skipping comment for ${prAuthor} - not their first PR (${authorPRCount} PRs found)`);
              }
            } catch (error) {
              console.error('Error checking PR history or posting comment:', error);
              // Don't fail the workflow, just log the error
            }
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
