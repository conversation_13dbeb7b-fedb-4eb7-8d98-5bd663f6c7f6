name: Add label on auto-merge enabled
permissions:
    pull-requests: write
on:
    pull_request_target:
        types:
            - auto_merge_enabled
jobs:
    add-label-on-auto-merge:
        runs-on: ubuntu-latest
        steps:
            -   name: Add label
                uses: actions/github-script@60a0d83039c74a4aee543508d2ffcb1c3799cdea # v7.0.1
                with:
                    script: |
                        github.rest.issues.addLabels({
                            owner: context.repo.owner,
                            repo: context.repo.repo,
                            issue_number: context.issue.number,
                            labels: ['ready']
                        })
                env:
                    GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
