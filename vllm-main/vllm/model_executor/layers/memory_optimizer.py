# SPDX-License-Identifier: Apache-2.0
# SPDX-FileCopyrightText: Copyright contributors to the vLLM project

"""
Memory access pattern optimizations for low batch size decode operations.
Specifically targets DeepSeek-R1 performance improvements.
"""

import logging
from typing import Dict, List, Optional, Tuple, Any
import torch
from collections import defaultdict

logger = logging.getLogger(__name__)


class MemoryAccessOptimizer:
    """
    Optimizer for memory access patterns in low batch size scenarios.
    
    Key optimizations:
    1. Expert weight prefetching for single token decode
    2. Cache-friendly memory layouts
    3. Reduced memory bandwidth usage
    4. Optimized tensor operations for small batches
    """
    
    def __init__(self, 
                 enable_prefetching: bool = True,
                 cache_size_mb: int = 64,
                 prefetch_threshold: int = 2):
        self.enable_prefetching = enable_prefetching
        self.cache_size_mb = cache_size_mb
        self.prefetch_threshold = prefetch_threshold
        
        # Expert weight cache for frequently used experts
        self.expert_weight_cache: Dict[int, Dict[str, torch.Tensor]] = {}
        self.cache_usage_count: Dict[int, int] = defaultdict(int)
        self.cache_size_bytes = 0
        self.max_cache_size_bytes = cache_size_mb * 1024 * 1024
        
        # Memory access pattern tracking
        self.access_patterns: Dict[str, List[int]] = defaultdict(list)
        self.prefetch_hits = 0
        self.prefetch_misses = 0
        
        logger.info(f"MemoryAccessOptimizer initialized: "
                   f"prefetching={enable_prefetching}, "
                   f"cache_size={cache_size_mb}MB")
    
    def should_prefetch_expert_weights(self, 
                                     expert_ids: List[int],
                                     batch_size: int) -> List[int]:
        """
        Determine which expert weights should be prefetched.
        
        Args:
            expert_ids: List of expert IDs that will be used
            batch_size: Current batch size
            
        Returns:
            List of expert IDs to prefetch
        """
        if not self.enable_prefetching or batch_size > self.prefetch_threshold:
            return []
        
        prefetch_candidates = []
        
        for expert_id in expert_ids:
            # Prefetch if this expert is frequently used
            if self.cache_usage_count[expert_id] >= 3:
                prefetch_candidates.append(expert_id)
            
            # For single token decode, always consider prefetching
            elif batch_size == 1:
                prefetch_candidates.append(expert_id)
        
        return prefetch_candidates
    
    def cache_expert_weights(self, 
                           expert_id: int,
                           weights: Dict[str, torch.Tensor]) -> bool:
        """
        Cache expert weights for faster access.
        
        Args:
            expert_id: Expert ID
            weights: Dictionary of weight tensors
            
        Returns:
            True if successfully cached, False if cache is full
        """
        if not self.enable_prefetching:
            return False
        
        # Calculate size of weights
        weight_size = sum(w.numel() * w.element_size() for w in weights.values())
        
        # Check if we have space in cache
        if self.cache_size_bytes + weight_size > self.max_cache_size_bytes:
            # Try to evict least used expert
            if not self._evict_least_used_expert(weight_size):
                return False
        
        # Cache the weights
        self.expert_weight_cache[expert_id] = {
            k: v.clone() for k, v in weights.items()
        }
        self.cache_size_bytes += weight_size
        self.cache_usage_count[expert_id] += 1
        
        return True
    
    def get_cached_expert_weights(self, expert_id: int) -> Optional[Dict[str, torch.Tensor]]:
        """Get cached expert weights if available."""
        if expert_id in self.expert_weight_cache:
            self.cache_usage_count[expert_id] += 1
            self.prefetch_hits += 1
            return self.expert_weight_cache[expert_id]
        
        self.prefetch_misses += 1
        return None
    
    def _evict_least_used_expert(self, required_size: int) -> bool:
        """
        Evict least used expert from cache to make space.
        
        Args:
            required_size: Size in bytes needed
            
        Returns:
            True if enough space was freed
        """
        if not self.expert_weight_cache:
            return False
        
        # Sort experts by usage count (ascending)
        experts_by_usage = sorted(
            self.cache_usage_count.items(),
            key=lambda x: x[1]
        )
        
        freed_space = 0
        for expert_id, _ in experts_by_usage:
            if expert_id in self.expert_weight_cache:
                # Calculate size of this expert's weights
                weights = self.expert_weight_cache[expert_id]
                expert_size = sum(w.numel() * w.element_size() for w in weights.values())
                
                # Remove from cache
                del self.expert_weight_cache[expert_id]
                del self.cache_usage_count[expert_id]
                
                freed_space += expert_size
                self.cache_size_bytes -= expert_size
                
                # Check if we've freed enough space
                if freed_space >= required_size:
                    return True
        
        return freed_space >= required_size
    
    def optimize_tensor_layout_for_small_batch(self, 
                                             tensor: torch.Tensor,
                                             batch_size: int) -> torch.Tensor:
        """
        Optimize tensor layout for small batch operations.
        
        Args:
            tensor: Input tensor
            batch_size: Current batch size
            
        Returns:
            Optimized tensor
        """
        if batch_size > 4:
            return tensor
        
        # For very small batches, ensure memory is contiguous
        if not tensor.is_contiguous():
            tensor = tensor.contiguous()
        
        # For single token, consider using different memory layout
        if batch_size == 1 and tensor.dim() >= 2:
            # Ensure optimal memory alignment for single token operations
            if tensor.stride(-1) != 1:
                tensor = tensor.contiguous()
        
        return tensor
    
    def prefetch_next_expert_weights(self, 
                                   current_expert_ids: List[int],
                                   predicted_next_experts: List[int]) -> None:
        """
        Prefetch weights for predicted next experts.
        
        Args:
            current_expert_ids: Currently active expert IDs
            predicted_next_experts: Predicted next expert IDs
        """
        if not self.enable_prefetching:
            return
        
        # Record access pattern for future prediction
        pattern_key = "_".join(map(str, sorted(current_expert_ids)))
        self.access_patterns[pattern_key].extend(predicted_next_experts)
        
        # Keep only recent patterns
        if len(self.access_patterns[pattern_key]) > 100:
            self.access_patterns[pattern_key] = self.access_patterns[pattern_key][-50:]
    
    def predict_next_experts(self, current_expert_ids: List[int]) -> List[int]:
        """
        Predict next expert IDs based on historical patterns.
        
        Args:
            current_expert_ids: Currently active expert IDs
            
        Returns:
            List of predicted next expert IDs
        """
        pattern_key = "_".join(map(str, sorted(current_expert_ids)))
        
        if pattern_key not in self.access_patterns:
            return []
        
        # Get most common next experts from historical data
        next_experts = self.access_patterns[pattern_key]
        if not next_experts:
            return []
        
        # Count frequency of each expert
        expert_counts = defaultdict(int)
        for expert_id in next_experts[-20:]:  # Use recent history
            expert_counts[expert_id] += 1
        
        # Return top 3 most frequent next experts
        top_experts = sorted(expert_counts.items(), key=lambda x: x[1], reverse=True)
        return [expert_id for expert_id, _ in top_experts[:3]]
    
    def optimize_memory_bandwidth(self, 
                                batch_size: int,
                                tensor_operations: List[Tuple[str, torch.Tensor]]) -> List[Tuple[str, torch.Tensor]]:
        """
        Optimize memory bandwidth usage for tensor operations.
        
        Args:
            batch_size: Current batch size
            tensor_operations: List of (operation_name, tensor) tuples
            
        Returns:
            Optimized list of tensor operations
        """
        if batch_size > 4:
            return tensor_operations
        
        optimized_ops = []
        
        for op_name, tensor in tensor_operations:
            # For small batches, ensure optimal memory access patterns
            if batch_size <= 2:
                # Use memory-efficient operations for very small batches
                if tensor.dim() >= 2 and tensor.size(0) <= 2:
                    # Ensure contiguous memory layout
                    tensor = self.optimize_tensor_layout_for_small_batch(tensor, batch_size)
            
            optimized_ops.append((op_name, tensor))
        
        return optimized_ops
    
    def get_cache_stats(self) -> Dict[str, Any]:
        """Get cache performance statistics."""
        total_requests = self.prefetch_hits + self.prefetch_misses
        hit_rate = self.prefetch_hits / max(1, total_requests)
        
        return {
            'cache_hit_rate': hit_rate,
            'cache_hits': self.prefetch_hits,
            'cache_misses': self.prefetch_misses,
            'cached_experts': len(self.expert_weight_cache),
            'cache_size_mb': self.cache_size_bytes / (1024 * 1024),
            'cache_utilization': self.cache_size_bytes / self.max_cache_size_bytes
        }
    
    def reset_cache(self) -> None:
        """Reset the expert weight cache."""
        self.expert_weight_cache.clear()
        self.cache_usage_count.clear()
        self.cache_size_bytes = 0
        self.prefetch_hits = 0
        self.prefetch_misses = 0
        self.access_patterns.clear()


# Global memory optimizer instance
_memory_optimizer: Optional[MemoryAccessOptimizer] = None


def get_memory_optimizer() -> MemoryAccessOptimizer:
    """Get the global memory access optimizer instance."""
    global _memory_optimizer
    if _memory_optimizer is None:
        _memory_optimizer = MemoryAccessOptimizer()
    return _memory_optimizer


def initialize_memory_optimizer(**kwargs) -> None:
    """Initialize the global memory access optimizer with custom parameters."""
    global _memory_optimizer
    _memory_optimizer = MemoryAccessOptimizer(**kwargs)
