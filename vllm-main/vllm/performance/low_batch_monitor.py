# SPDX-License-Identifier: Apache-2.0
# SPDX-FileCopyrightText: Copyright contributors to the vLLM project

"""
Performance monitoring and metrics for low batch size optimizations.
Specifically designed for tracking DeepSeek-R1 performance improvements.
"""

import time
import logging
from typing import Dict, List, Optional, Any, Tuple
from collections import defaultdict, deque
from dataclasses import dataclass, field
import threading
import json

logger = logging.getLogger(__name__)


@dataclass
class PerformanceMetrics:
    """Container for performance metrics."""
    
    # Timing metrics
    decode_latency_ms: List[float] = field(default_factory=list)
    sync_latency_ms: List[float] = field(default_factory=list)
    expert_routing_latency_ms: List[float] = field(default_factory=list)
    memory_access_latency_ms: List[float] = field(default_factory=list)
    
    # Throughput metrics
    tokens_per_second: List[float] = field(default_factory=list)
    requests_per_second: List[float] = field(default_factory=list)
    
    # Cache metrics
    expert_cache_hit_rate: List[float] = field(default_factory=list)
    memory_cache_hit_rate: List[float] = field(default_factory=list)
    routing_cache_hit_rate: List[float] = field(default_factory=list)
    
    # Resource utilization
    gpu_utilization: List[float] = field(default_factory=list)
    memory_utilization: List[float] = field(default_factory=list)
    communication_overhead: List[float] = field(default_factory=list)
    
    # Optimization effectiveness
    dummy_forwards_skipped: int = 0
    sync_operations_skipped: int = 0
    expert_prefetch_hits: int = 0
    single_token_optimizations_used: int = 0
    
    def add_decode_latency(self, latency_ms: float):
        """Add decode latency measurement."""
        self.decode_latency_ms.append(latency_ms)
        self._trim_list(self.decode_latency_ms)
    
    def add_throughput(self, tokens_per_sec: float, requests_per_sec: float):
        """Add throughput measurements."""
        self.tokens_per_second.append(tokens_per_sec)
        self.requests_per_second.append(requests_per_sec)
        self._trim_list(self.tokens_per_second)
        self._trim_list(self.requests_per_second)
    
    def add_cache_metrics(self, expert_hit_rate: float, memory_hit_rate: float, routing_hit_rate: float):
        """Add cache hit rate measurements."""
        self.expert_cache_hit_rate.append(expert_hit_rate)
        self.memory_cache_hit_rate.append(memory_hit_rate)
        self.routing_cache_hit_rate.append(routing_hit_rate)
        self._trim_list(self.expert_cache_hit_rate)
        self._trim_list(self.memory_cache_hit_rate)
        self._trim_list(self.routing_cache_hit_rate)
    
    def _trim_list(self, lst: List[float], max_size: int = 1000):
        """Trim list to maximum size to prevent memory growth."""
        if len(lst) > max_size:
            lst[:] = lst[-max_size//2:]
    
    def get_summary(self) -> Dict[str, Any]:
        """Get summary statistics."""
        def safe_avg(lst):
            return sum(lst) / len(lst) if lst else 0.0
        
        def safe_percentile(lst, p):
            if not lst:
                return 0.0
            sorted_lst = sorted(lst)
            idx = int(len(sorted_lst) * p / 100)
            return sorted_lst[min(idx, len(sorted_lst) - 1)]
        
        return {
            'decode_latency': {
                'avg_ms': safe_avg(self.decode_latency_ms),
                'p50_ms': safe_percentile(self.decode_latency_ms, 50),
                'p95_ms': safe_percentile(self.decode_latency_ms, 95),
                'p99_ms': safe_percentile(self.decode_latency_ms, 99),
            },
            'throughput': {
                'avg_tokens_per_sec': safe_avg(self.tokens_per_second),
                'avg_requests_per_sec': safe_avg(self.requests_per_second),
            },
            'cache_performance': {
                'expert_hit_rate': safe_avg(self.expert_cache_hit_rate),
                'memory_hit_rate': safe_avg(self.memory_cache_hit_rate),
                'routing_hit_rate': safe_avg(self.routing_cache_hit_rate),
            },
            'optimizations': {
                'dummy_forwards_skipped': self.dummy_forwards_skipped,
                'sync_operations_skipped': self.sync_operations_skipped,
                'expert_prefetch_hits': self.expert_prefetch_hits,
                'single_token_optimizations': self.single_token_optimizations_used,
            }
        }


class LowBatchPerformanceMonitor:
    """
    Performance monitor for low batch size optimizations.
    
    Tracks various metrics to measure the effectiveness of optimizations
    and identify further improvement opportunities.
    """
    
    def __init__(self, 
                 enable_detailed_logging: bool = False,
                 log_interval_seconds: int = 60,
                 max_history_size: int = 10000):
        self.enable_detailed_logging = enable_detailed_logging
        self.log_interval_seconds = log_interval_seconds
        self.max_history_size = max_history_size
        
        # Metrics storage
        self.metrics = PerformanceMetrics()
        self.batch_size_metrics: Dict[int, PerformanceMetrics] = defaultdict(PerformanceMetrics)
        
        # Request tracking
        self.active_requests: Dict[str, float] = {}  # request_id -> start_time
        self.completed_requests: deque = deque(maxlen=max_history_size)
        
        # Performance tracking
        self.last_log_time = time.time()
        self.total_requests = 0
        self.total_tokens = 0
        
        # Thread safety
        self._lock = threading.Lock()
        
        logger.info(f"LowBatchPerformanceMonitor initialized: "
                   f"detailed_logging={enable_detailed_logging}, "
                   f"log_interval={log_interval_seconds}s")
    
    def start_request(self, request_id: str, batch_size: int) -> None:
        """Start tracking a request."""
        with self._lock:
            self.active_requests[request_id] = time.time()
            self.total_requests += 1
    
    def end_request(self, 
                   request_id: str, 
                   batch_size: int,
                   num_tokens: int,
                   optimization_stats: Optional[Dict[str, Any]] = None) -> None:
        """End tracking a request and record metrics."""
        with self._lock:
            if request_id not in self.active_requests:
                return
            
            start_time = self.active_requests.pop(request_id)
            end_time = time.time()
            latency_ms = (end_time - start_time) * 1000
            
            # Record metrics
            self.metrics.add_decode_latency(latency_ms)
            self.batch_size_metrics[batch_size].add_decode_latency(latency_ms)
            
            self.total_tokens += num_tokens
            
            # Record optimization stats if provided
            if optimization_stats:
                self._record_optimization_stats(optimization_stats, batch_size)
            
            # Record completed request
            self.completed_requests.append({
                'request_id': request_id,
                'batch_size': batch_size,
                'num_tokens': num_tokens,
                'latency_ms': latency_ms,
                'timestamp': end_time,
                'optimization_stats': optimization_stats
            })
            
            # Log periodically
            self._maybe_log_stats()
    
    def record_cache_stats(self, 
                          expert_hit_rate: float,
                          memory_hit_rate: float,
                          routing_hit_rate: float,
                          batch_size: int = 0) -> None:
        """Record cache performance statistics."""
        with self._lock:
            self.metrics.add_cache_metrics(expert_hit_rate, memory_hit_rate, routing_hit_rate)
            if batch_size > 0:
                self.batch_size_metrics[batch_size].add_cache_metrics(
                    expert_hit_rate, memory_hit_rate, routing_hit_rate)
    
    def record_optimization_event(self, event_type: str, batch_size: int = 0) -> None:
        """Record an optimization event."""
        with self._lock:
            if event_type == "dummy_forward_skipped":
                self.metrics.dummy_forwards_skipped += 1
                if batch_size > 0:
                    self.batch_size_metrics[batch_size].dummy_forwards_skipped += 1
            
            elif event_type == "sync_operation_skipped":
                self.metrics.sync_operations_skipped += 1
                if batch_size > 0:
                    self.batch_size_metrics[batch_size].sync_operations_skipped += 1
            
            elif event_type == "expert_prefetch_hit":
                self.metrics.expert_prefetch_hits += 1
                if batch_size > 0:
                    self.batch_size_metrics[batch_size].expert_prefetch_hits += 1
            
            elif event_type == "single_token_optimization":
                self.metrics.single_token_optimizations_used += 1
                if batch_size > 0:
                    self.batch_size_metrics[batch_size].single_token_optimizations_used += 1
    
    def _record_optimization_stats(self, stats: Dict[str, Any], batch_size: int) -> None:
        """Record optimization statistics from a request."""
        # Record various optimization events based on stats
        if stats.get('used_expert_cache', False):
            self.record_optimization_event("expert_prefetch_hit", batch_size)
        
        if stats.get('skipped_dummy_forward', False):
            self.record_optimization_event("dummy_forward_skipped", batch_size)
        
        if stats.get('skipped_sync', False):
            self.record_optimization_event("sync_operation_skipped", batch_size)
        
        if stats.get('used_single_token_opt', False):
            self.record_optimization_event("single_token_optimization", batch_size)
    
    def _maybe_log_stats(self) -> None:
        """Log statistics if enough time has passed."""
        current_time = time.time()
        if current_time - self.last_log_time >= self.log_interval_seconds:
            self._log_performance_summary()
            self.last_log_time = current_time
    
    def _log_performance_summary(self) -> None:
        """Log a performance summary."""
        if not self.enable_detailed_logging:
            return
        
        summary = self.get_performance_summary()
        logger.info(f"Low batch optimization performance summary: {json.dumps(summary, indent=2)}")
    
    def get_performance_summary(self) -> Dict[str, Any]:
        """Get comprehensive performance summary."""
        with self._lock:
            current_time = time.time()
            time_window = current_time - self.last_log_time
            
            # Calculate throughput
            recent_requests = [r for r in self.completed_requests 
                             if current_time - r['timestamp'] <= time_window]
            
            requests_per_sec = len(recent_requests) / max(time_window, 1)
            tokens_per_sec = sum(r['num_tokens'] for r in recent_requests) / max(time_window, 1)
            
            self.metrics.add_throughput(tokens_per_sec, requests_per_sec)
            
            # Overall summary
            summary = {
                'overall': self.metrics.get_summary(),
                'by_batch_size': {},
                'system': {
                    'total_requests': self.total_requests,
                    'total_tokens': self.total_tokens,
                    'active_requests': len(self.active_requests),
                    'monitoring_duration_hours': (current_time - self.last_log_time) / 3600,
                }
            }
            
            # Per-batch-size breakdown
            for batch_size, metrics in self.batch_size_metrics.items():
                summary['by_batch_size'][batch_size] = metrics.get_summary()
            
            return summary
    
    def reset_stats(self) -> None:
        """Reset all statistics."""
        with self._lock:
            self.metrics = PerformanceMetrics()
            self.batch_size_metrics.clear()
            self.active_requests.clear()
            self.completed_requests.clear()
            self.total_requests = 0
            self.total_tokens = 0
            self.last_log_time = time.time()
    
    def export_metrics(self, filepath: str) -> None:
        """Export metrics to a JSON file."""
        summary = self.get_performance_summary()
        with open(filepath, 'w') as f:
            json.dump(summary, f, indent=2)
        logger.info(f"Performance metrics exported to {filepath}")


# Global monitor instance
_performance_monitor: Optional[LowBatchPerformanceMonitor] = None


def get_performance_monitor() -> LowBatchPerformanceMonitor:
    """Get the global performance monitor instance."""
    global _performance_monitor
    if _performance_monitor is None:
        _performance_monitor = LowBatchPerformanceMonitor()
    return _performance_monitor


def initialize_performance_monitor(**kwargs) -> None:
    """Initialize the global performance monitor with custom parameters."""
    global _performance_monitor
    _performance_monitor = LowBatchPerformanceMonitor(**kwargs)
