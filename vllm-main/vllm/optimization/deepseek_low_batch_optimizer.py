# SPDX-License-Identifier: Apache-2.0
# SPDX-FileCopyrightText: Copyright contributors to the vLLM project

"""
Main integration module for DeepSeek-R1 low batch size optimizations.
This module coordinates all optimization components to solve the batch size 1 decode performance issue.
"""

import logging
import time
from typing import Dict, List, Optional, Any, Tuple
import torch

from vllm.config.low_batch_optimization import (
    get_low_batch_optimization_config,
    get_microbatching_config,
    should_apply_deepseek_optimizations
)
from vllm.distributed.low_batch_optimizer import get_low_batch_optimizer
from vllm.distributed.smart_expert_scheduler import get_smart_expert_scheduler
from vllm.model_executor.layers.memory_optimizer import get_memory_optimizer
from vllm.performance.low_batch_monitor import get_performance_monitor

logger = logging.getLogger(__name__)


class DeepSeekLowBatchOptimizer:
    """
    Main coordinator for DeepSeek-R1 low batch size optimizations.
    
    This class integrates all optimization components to provide a unified
    interface for optimizing batch size 1 decode performance.
    """
    
    def __init__(self, model_name: str, parallel_config: Any):
        self.model_name = model_name
        self.parallel_config = parallel_config
        
        # Configuration
        self.config = get_low_batch_optimization_config()
        self.microbatch_config = get_microbatching_config()
        
        # Check if optimizations should be enabled for this model
        self.enabled = should_apply_deepseek_optimizations(model_name)
        
        if self.enabled:
            # Initialize optimization components
            self._initialize_optimizers()
            logger.info(f"DeepSeek low batch optimizations enabled for {model_name}")
        else:
            logger.info(f"DeepSeek low batch optimizations disabled for {model_name}")
    
    def _initialize_optimizers(self):
        """Initialize all optimization components."""
        # Initialize low batch optimizer
        self.low_batch_optimizer = get_low_batch_optimizer()
        
        # Initialize smart expert scheduler if we have expert parallelism
        if hasattr(self.parallel_config, 'expert_parallel_size'):
            ep_size = getattr(self.parallel_config, 'expert_parallel_size', 1)
            dp_size = getattr(self.parallel_config, 'data_parallel_size', 1)
            
            if ep_size > 1 or dp_size > 1:
                from vllm.distributed.smart_expert_scheduler import initialize_smart_expert_scheduler
                # Estimate number of experts (DeepSeek-V3 has ~256 experts)
                num_experts = 256  # Default for DeepSeek models
                initialize_smart_expert_scheduler(
                    num_experts=num_experts,
                    dp_size=dp_size,
                    ep_size=ep_size
                )
                self.smart_scheduler = get_smart_expert_scheduler()
        
        # Initialize memory optimizer
        self.memory_optimizer = get_memory_optimizer()
        
        # Initialize performance monitor
        self.performance_monitor = get_performance_monitor()
    
    def optimize_forward_pass(self, 
                            batch_size: int,
                            hidden_states: torch.Tensor,
                            expert_ids: Optional[List[int]] = None,
                            request_id: Optional[str] = None) -> Dict[str, Any]:
        """
        Apply optimizations for a forward pass.
        
        Args:
            batch_size: Current batch size
            hidden_states: Input hidden states
            expert_ids: List of expert IDs being used
            request_id: Unique request identifier
            
        Returns:
            Dictionary with optimization decisions and metadata
        """
        if not self.enabled:
            return {'optimizations_applied': []}
        
        start_time = time.time()
        optimizations = []
        optimization_metadata = {}
        
        # Start performance tracking
        if request_id:
            self.performance_monitor.start_request(request_id, batch_size)
        
        # Apply single token optimizations
        if self.config.should_use_single_token_optimization(batch_size):
            single_token_opts = self._apply_single_token_optimizations(
                hidden_states, expert_ids)
            optimizations.extend(single_token_opts['optimizations'])
            optimization_metadata.update(single_token_opts['metadata'])
        
        # Apply expert scheduling optimizations
        if (self.config.should_use_smart_scheduling(batch_size) and 
            hasattr(self, 'smart_scheduler') and self.smart_scheduler):
            expert_opts = self._apply_expert_optimizations(
                batch_size, expert_ids)
            optimizations.extend(expert_opts['optimizations'])
            optimization_metadata.update(expert_opts['metadata'])
        
        # Apply memory optimizations
        if self.config.should_optimize_memory_access(batch_size):
            memory_opts = self._apply_memory_optimizations(
                batch_size, hidden_states)
            optimizations.extend(memory_opts['optimizations'])
            optimization_metadata.update(memory_opts['metadata'])
        
        # Apply communication optimizations
        if batch_size <= self.config.small_batch_threshold:
            comm_opts = self._apply_communication_optimizations(batch_size)
            optimizations.extend(comm_opts['optimizations'])
            optimization_metadata.update(comm_opts['metadata'])
        
        optimization_time = (time.time() - start_time) * 1000  # ms
        
        return {
            'optimizations_applied': optimizations,
            'metadata': optimization_metadata,
            'optimization_time_ms': optimization_time,
            'batch_size': batch_size
        }
    
    def _apply_single_token_optimizations(self, 
                                        hidden_states: torch.Tensor,
                                        expert_ids: Optional[List[int]]) -> Dict[str, Any]:
        """Apply optimizations specific to single token decode."""
        optimizations = []
        metadata = {}
        
        # Expert routing cache
        if self.config.enable_expert_routing_cache and expert_ids:
            input_hash = hash(hidden_states.data_ptr())
            cached_routing = self.low_batch_optimizer.get_cached_expert_routing(input_hash)
            if cached_routing is not None:
                optimizations.append('expert_routing_cache_hit')
                metadata['used_cached_routing'] = True
            else:
                metadata['used_cached_routing'] = False
        
        # Shared expert only optimization
        if self.config.shared_expert_only_for_single_token:
            optimizations.append('shared_expert_only')
            metadata['skip_expert_routing'] = True
        
        # Memory layout optimization
        optimized_states = self.memory_optimizer.optimize_tensor_layout_for_small_batch(
            hidden_states, 1)
        if optimized_states is not hidden_states:
            optimizations.append('memory_layout_optimization')
            metadata['memory_layout_optimized'] = True
        
        return {'optimizations': optimizations, 'metadata': metadata}
    
    def _apply_expert_optimizations(self, 
                                  batch_size: int,
                                  expert_ids: Optional[List[int]]) -> Dict[str, Any]:
        """Apply expert scheduling optimizations."""
        optimizations = []
        metadata = {}
        
        if not expert_ids:
            return {'optimizations': optimizations, 'metadata': metadata}
        
        # Check if dummy forwards can be skipped
        dp_size = getattr(self.parallel_config, 'data_parallel_size', 1)
        if dp_size > 1:
            skip_decisions = self.smart_scheduler.should_eliminate_dummy_forwards(
                batch_size, [0])  # Assume rank 0 is active
            
            skipped_ranks = sum(1 for skip in skip_decisions.values() if skip)
            if skipped_ranks > 0:
                optimizations.append('dummy_forward_elimination')
                metadata['dummy_forwards_skipped'] = skipped_ranks
        
        # Optimize communication pattern
        comm_pattern = self.smart_scheduler.optimize_communication_pattern(
            batch_size, {0: expert_ids})
        if comm_pattern != 'standard_all_to_all':
            optimizations.append(f'communication_pattern_{comm_pattern}')
            metadata['communication_pattern'] = comm_pattern
        
        # Expert caching
        if self.config.should_use_expert_caching(batch_size):
            for expert_id in expert_ids:
                cached_weights = self.memory_optimizer.get_cached_expert_weights(expert_id)
                if cached_weights:
                    optimizations.append('expert_weight_cache_hit')
                    metadata['expert_cache_hits'] = metadata.get('expert_cache_hits', 0) + 1
        
        return {'optimizations': optimizations, 'metadata': metadata}
    
    def _apply_memory_optimizations(self, 
                                  batch_size: int,
                                  hidden_states: torch.Tensor) -> Dict[str, Any]:
        """Apply memory access optimizations."""
        optimizations = []
        metadata = {}
        
        # Tensor layout optimization
        optimized_states = self.memory_optimizer.optimize_tensor_layout_for_small_batch(
            hidden_states, batch_size)
        if optimized_states is not hidden_states:
            optimizations.append('tensor_layout_optimization')
            metadata['tensor_layout_optimized'] = True
        
        # Memory bandwidth optimization
        if batch_size <= 2:
            optimizations.append('memory_bandwidth_optimization')
            metadata['memory_bandwidth_optimized'] = True
        
        return {'optimizations': optimizations, 'metadata': metadata}
    
    def _apply_communication_optimizations(self, batch_size: int) -> Dict[str, Any]:
        """Apply communication optimizations."""
        optimizations = []
        metadata = {}
        
        # Skip synchronization for very small batches
        if self.low_batch_optimizer.should_skip_synchronization(batch_size, 1):
            optimizations.append('synchronization_skip')
            metadata['synchronization_skipped'] = True
        
        # Use async communication
        if batch_size <= 4:
            optimizations.append('async_communication')
            metadata['async_communication'] = True
        
        return {'optimizations': optimizations, 'metadata': metadata}
    
    def finalize_request(self, 
                        request_id: str,
                        batch_size: int,
                        num_tokens: int,
                        optimization_stats: Dict[str, Any]) -> None:
        """Finalize request and record performance metrics."""
        if not self.enabled or not request_id:
            return
        
        # End performance tracking
        self.performance_monitor.end_request(
            request_id, batch_size, num_tokens, optimization_stats)
        
        # Update expert scheduler with activity
        if (hasattr(self, 'smart_scheduler') and self.smart_scheduler and 
            'expert_ids' in optimization_stats):
            expert_ids = optimization_stats['expert_ids']
            self.smart_scheduler.update_rank_activity(0, expert_ids, batch_size)
    
    def get_performance_summary(self) -> Dict[str, Any]:
        """Get comprehensive performance summary."""
        if not self.enabled:
            return {'optimizations_enabled': False}
        
        summary = {
            'optimizations_enabled': True,
            'model_name': self.model_name,
            'config': self.config.get_optimization_summary(),
        }
        
        # Add performance metrics
        summary['performance'] = self.performance_monitor.get_performance_summary()
        
        # Add component-specific stats
        if hasattr(self, 'smart_scheduler') and self.smart_scheduler:
            summary['expert_scheduling'] = self.smart_scheduler.get_performance_stats()
        
        summary['memory_optimization'] = self.memory_optimizer.get_cache_stats()
        summary['low_batch_optimization'] = self.low_batch_optimizer.get_performance_stats()
        
        return summary


# Global optimizer instance
_deepseek_optimizer: Optional[DeepSeekLowBatchOptimizer] = None


def get_deepseek_optimizer() -> Optional[DeepSeekLowBatchOptimizer]:
    """Get the global DeepSeek optimizer instance."""
    return _deepseek_optimizer


def initialize_deepseek_optimizer(model_name: str, parallel_config: Any) -> None:
    """Initialize the global DeepSeek optimizer."""
    global _deepseek_optimizer
    _deepseek_optimizer = DeepSeekLowBatchOptimizer(model_name, parallel_config)


def optimize_deepseek_forward_pass(batch_size: int,
                                 hidden_states: torch.Tensor,
                                 expert_ids: Optional[List[int]] = None,
                                 request_id: Optional[str] = None) -> Dict[str, Any]:
    """Convenience function to apply DeepSeek optimizations."""
    optimizer = get_deepseek_optimizer()
    if optimizer:
        return optimizer.optimize_forward_pass(batch_size, hidden_states, expert_ids, request_id)
    return {'optimizations_applied': []}
