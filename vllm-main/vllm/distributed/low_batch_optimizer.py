# SPDX-License-Identifier: Apache-2.0
# SPDX-FileCopyrightText: Copyright contributors to the vLLM project

"""
Optimizations for low batch size decode operations in DP+EP configurations.
Specifically targets DeepSeek-R1 performance issues with batch size 1.
"""

import logging
import time
from typing import Optional, Dict, Any, List
import torch
from torch.distributed import ProcessGroup

from vllm.distributed.parallel_state import (
    get_data_parallel_group, 
    get_expert_parallel_group,
    get_tensor_model_parallel_group
)
from vllm.envs import VLLM_USE_V1

logger = logging.getLogger(__name__)


class LowBatchSizeOptimizer:
    """
    Optimizer for low batch size decode operations in DP+EP configurations.
    
    Key optimizations:
    1. Conditional synchronization based on batch size
    2. Expert routing cache for single token decode
    3. Optimized communication patterns
    4. Smart dummy forward pass elimination
    """
    
    def __init__(self, 
                 enable_single_token_optimization: bool = True,
                 expert_cache_size: int = 1024,
                 min_batch_for_sync: int = 2):
        self.enable_single_token_optimization = enable_single_token_optimization
        self.expert_cache_size = expert_cache_size
        self.min_batch_for_sync = min_batch_for_sync
        
        # Expert routing cache for single token optimization
        self._expert_routing_cache: Dict[int, torch.Tensor] = {}
        self._cache_hits = 0
        self._cache_misses = 0
        
        # Performance tracking
        self._decode_times: List[float] = []
        self._sync_times: List[float] = []
        
        logger.info(f"LowBatchSizeOptimizer initialized with "
                   f"single_token_opt={enable_single_token_optimization}, "
                   f"cache_size={expert_cache_size}, "
                   f"min_batch_sync={min_batch_for_sync}")
    
    def should_skip_synchronization(self, batch_size: int, num_active_ranks: int) -> bool:
        """
        Determine if synchronization can be skipped for this batch.
        
        Args:
            batch_size: Current batch size
            num_active_ranks: Number of DP ranks with active requests
            
        Returns:
            True if synchronization can be safely skipped
        """
        if not self.enable_single_token_optimization:
            return False
            
        # Skip sync for single token decode when only one rank is active
        if batch_size == 1 and num_active_ranks == 1:
            return True
            
        # Skip sync for very small batches if most ranks are idle
        if batch_size < self.min_batch_for_sync and num_active_ranks <= 2:
            return True
            
        return False
    
    def get_cached_expert_routing(self, input_hash: int) -> Optional[torch.Tensor]:
        """
        Get cached expert routing for repeated single token patterns.
        
        Args:
            input_hash: Hash of the input token/hidden state
            
        Returns:
            Cached routing tensor if available, None otherwise
        """
        if not self.enable_single_token_optimization:
            return None
            
        if input_hash in self._expert_routing_cache:
            self._cache_hits += 1
            return self._expert_routing_cache[input_hash]
        
        self._cache_misses += 1
        return None
    
    def cache_expert_routing(self, input_hash: int, routing: torch.Tensor) -> None:
        """
        Cache expert routing for future use.
        
        Args:
            input_hash: Hash of the input token/hidden state
            routing: Expert routing tensor to cache
        """
        if not self.enable_single_token_optimization:
            return
            
        # Implement LRU-style cache eviction
        if len(self._expert_routing_cache) >= self.expert_cache_size:
            # Remove oldest entry (simple FIFO for now)
            oldest_key = next(iter(self._expert_routing_cache))
            del self._expert_routing_cache[oldest_key]
        
        self._expert_routing_cache[input_hash] = routing.clone()
    
    def optimize_communication_pattern(self, 
                                     batch_size: int,
                                     expert_ids: torch.Tensor,
                                     dp_group: ProcessGroup,
                                     ep_group: ProcessGroup) -> Dict[str, Any]:
        """
        Optimize communication pattern based on batch size and expert distribution.
        
        Args:
            batch_size: Current batch size
            expert_ids: Selected expert IDs
            dp_group: Data parallel process group
            ep_group: Expert parallel process group
            
        Returns:
            Dictionary with optimization parameters
        """
        optimizations = {
            'use_async_comm': False,
            'skip_empty_experts': False,
            'use_point_to_point': False,
            'batch_communications': False
        }
        
        if batch_size == 1:
            # For single token, use point-to-point communication
            optimizations['use_point_to_point'] = True
            optimizations['skip_empty_experts'] = True
            
        elif batch_size <= 4:
            # For small batches, use async communication
            optimizations['use_async_comm'] = True
            optimizations['skip_empty_experts'] = True
            
        else:
            # For larger batches, use standard batched communication
            optimizations['batch_communications'] = True
        
        return optimizations
    
    def should_eliminate_dummy_forward(self, 
                                     active_ranks: List[int],
                                     total_ranks: int,
                                     batch_size: int) -> bool:
        """
        Determine if dummy forward passes can be eliminated.
        
        Args:
            active_ranks: List of ranks with active requests
            total_ranks: Total number of DP ranks
            batch_size: Current batch size
            
        Returns:
            True if dummy forwards can be eliminated
        """
        if not self.enable_single_token_optimization:
            return False
        
        # If only one rank is active and batch size is 1, 
        # we might be able to skip dummy forwards on other ranks
        if len(active_ranks) == 1 and batch_size == 1:
            return True
        
        # If most ranks are idle, consider eliminating dummy forwards
        active_ratio = len(active_ranks) / total_ranks
        if active_ratio < 0.3 and batch_size <= 2:
            return True
        
        return False
    
    def record_decode_time(self, decode_time: float) -> None:
        """Record decode time for performance tracking."""
        self._decode_times.append(decode_time)
        # Keep only recent measurements
        if len(self._decode_times) > 1000:
            self._decode_times = self._decode_times[-500:]
    
    def record_sync_time(self, sync_time: float) -> None:
        """Record synchronization time for performance tracking."""
        self._sync_times.append(sync_time)
        # Keep only recent measurements
        if len(self._sync_times) > 1000:
            self._sync_times = self._sync_times[-500:]
    
    def get_performance_stats(self) -> Dict[str, Any]:
        """Get performance statistics."""
        stats = {
            'cache_hit_rate': self._cache_hits / max(1, self._cache_hits + self._cache_misses),
            'cache_hits': self._cache_hits,
            'cache_misses': self._cache_misses,
            'avg_decode_time': sum(self._decode_times) / max(1, len(self._decode_times)),
            'avg_sync_time': sum(self._sync_times) / max(1, len(self._sync_times)),
            'num_decode_samples': len(self._decode_times),
            'num_sync_samples': len(self._sync_times)
        }
        return stats
    
    def reset_stats(self) -> None:
        """Reset performance statistics."""
        self._cache_hits = 0
        self._cache_misses = 0
        self._decode_times.clear()
        self._sync_times.clear()


# Global optimizer instance
_low_batch_optimizer: Optional[LowBatchSizeOptimizer] = None


def get_low_batch_optimizer() -> LowBatchSizeOptimizer:
    """Get the global low batch size optimizer instance."""
    global _low_batch_optimizer
    if _low_batch_optimizer is None:
        _low_batch_optimizer = LowBatchSizeOptimizer()
    return _low_batch_optimizer


def initialize_low_batch_optimizer(**kwargs) -> None:
    """Initialize the global low batch size optimizer with custom parameters."""
    global _low_batch_optimizer
    _low_batch_optimizer = LowBatchSizeOptimizer(**kwargs)


def optimized_tensor_model_parallel_all_reduce(input_tensor: torch.Tensor,
                                              batch_size: int = None) -> torch.Tensor:
    """
    Optimized all-reduce that considers batch size for performance.
    
    Args:
        input_tensor: Tensor to reduce
        batch_size: Current batch size (for optimization decisions)
        
    Returns:
        Reduced tensor
    """
    from vllm.distributed.communication_op import tensor_model_parallel_all_reduce
    
    optimizer = get_low_batch_optimizer()
    
    # For single token decode, consider if we can skip or optimize the all-reduce
    if batch_size == 1 and optimizer.enable_single_token_optimization:
        # Check if we're in a configuration where all-reduce can be optimized
        tp_group = get_tensor_model_parallel_group()
        if tp_group is None or tp_group.size() == 1:
            # No tensor parallelism, no need for all-reduce
            return input_tensor
    
    # Record timing for performance tracking
    start_time = time.perf_counter()
    result = tensor_model_parallel_all_reduce(input_tensor)
    sync_time = time.perf_counter() - start_time
    
    optimizer.record_sync_time(sync_time)
    
    return result
