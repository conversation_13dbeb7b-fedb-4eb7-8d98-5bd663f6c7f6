# SPDX-License-Identifier: Apache-2.0
# SPDX-FileCopyrightText: Copyright contributors to the vLLM project

from typing import Any, Optional, Union

import torch
import torch.distributed

from .parallel_state import get_tp_group


def tensor_model_parallel_all_reduce(input_: torch.Tensor,
                                     batch_size_hint: Optional[int] = None) -> torch.Tensor:
    """
    All-reduce the input tensor across model parallel group.

    Args:
        input_: Tensor to reduce
        batch_size_hint: Hint about batch size for optimization decisions
    """
    # Apply low batch size optimizations if available
    if batch_size_hint is not None and batch_size_hint <= 4:
        try:
            from vllm.distributed.low_batch_optimizer import optimized_tensor_model_parallel_all_reduce
            return optimized_tensor_model_parallel_all_reduce(input_, batch_size_hint)
        except ImportError:
            # Fallback to standard implementation
            pass

    return get_tp_group().all_reduce(input_)


def tensor_model_parallel_all_gather(input_: torch.Tensor,
                                     dim: int = -1) -> torch.Tensor:
    """All-gather the input tensor across model parallel group."""
    return get_tp_group().all_gather(input_, dim)


def tensor_model_parallel_reduce_scatter(input_: torch.Tensor,
                                         dim: int = -1) -> torch.Tensor:
    """Reduce-Scatter the input tensor across model parallel group."""
    return get_tp_group().reduce_scatter(input_, dim)


def tensor_model_parallel_gather(input_: torch.Tensor,
                                 dst: int = 0,
                                 dim: int = -1) -> Optional[torch.Tensor]:
    """Gather the input tensor across model parallel group."""
    return get_tp_group().gather(input_, dst, dim)


def broadcast_tensor_dict(tensor_dict: Optional[dict[Any, Union[torch.Tensor,
                                                                Any]]] = None,
                          src: int = 0):
    if not torch.distributed.is_initialized():
        return tensor_dict
    return get_tp_group().broadcast_tensor_dict(tensor_dict, src)
