# SPDX-License-Identifier: Apache-2.0
# SPDX-FileCopyrightText: Copyright contributors to the vLLM project

"""
Smart Expert Scheduling for optimizing low batch size decode in DP+EP configurations.
Specifically designed to address DeepSeek-R1 performance issues.
"""

import logging
import time
from typing import Dict, List, Optional, Set, Tuple
import torch
from collections import defaultdict, deque

from vllm.distributed.parallel_state import (
    get_data_parallel_group,
    get_expert_parallel_group,
    get_data_parallel_rank,
    get_expert_parallel_rank
)

logger = logging.getLogger(__name__)


class ExpertLoadTracker:
    """Track expert load across DP ranks for intelligent scheduling."""
    
    def __init__(self, num_experts: int, window_size: int = 100):
        self.num_experts = num_experts
        self.window_size = window_size
        
        # Track expert usage per rank
        self.expert_usage: Dict[int, deque] = defaultdict(lambda: deque(maxlen=window_size))
        self.expert_load: Dict[int, float] = defaultdict(float)
        
        # Track which ranks are active
        self.active_ranks: Set[int] = set()
        self.last_activity: Dict[int, float] = {}
        
        # Performance metrics
        self.load_balance_score = 1.0
        self.communication_overhead = 0.0
    
    def update_expert_usage(self, rank: int, expert_ids: List[int], batch_size: int):
        """Update expert usage for a specific rank."""
        current_time = time.time()
        self.active_ranks.add(rank)
        self.last_activity[rank] = current_time
        
        # Record expert usage
        usage_record = {
            'expert_ids': expert_ids,
            'batch_size': batch_size,
            'timestamp': current_time
        }
        self.expert_usage[rank].append(usage_record)
        
        # Update load metrics
        self._update_load_metrics(rank)
    
    def _update_load_metrics(self, rank: int):
        """Update load balance metrics for a rank."""
        if not self.expert_usage[rank]:
            return
        
        # Calculate recent expert load
        recent_usage = list(self.expert_usage[rank])[-10:]  # Last 10 requests
        expert_counts = defaultdict(int)
        total_tokens = 0
        
        for usage in recent_usage:
            for expert_id in usage['expert_ids']:
                expert_counts[expert_id] += usage['batch_size']
            total_tokens += usage['batch_size']
        
        # Update load for this rank
        if total_tokens > 0:
            self.expert_load[rank] = len(expert_counts) / self.num_experts
    
    def get_idle_ranks(self, timeout_seconds: float = 1.0) -> List[int]:
        """Get ranks that have been idle for more than timeout_seconds."""
        current_time = time.time()
        idle_ranks = []
        
        for rank in list(self.active_ranks):
            if current_time - self.last_activity.get(rank, 0) > timeout_seconds:
                idle_ranks.append(rank)
                self.active_ranks.discard(rank)
        
        return idle_ranks
    
    def should_skip_dummy_forward(self, rank: int, batch_size: int) -> bool:
        """Determine if dummy forward can be skipped for this rank."""
        # Skip dummy forwards for idle ranks when batch size is small
        if batch_size <= 2 and rank not in self.active_ranks:
            return True
        
        # Skip if this rank has very low recent activity
        if self.expert_load.get(rank, 0) < 0.1 and batch_size == 1:
            return True
        
        return False


class SmartExpertScheduler:
    """
    Smart scheduler for expert parallel operations in low batch size scenarios.
    
    Key optimizations:
    1. Minimize dummy forward passes
    2. Optimize communication patterns
    3. Balance expert load intelligently
    4. Cache expert routing decisions
    """
    
    def __init__(self, 
                 num_experts: int,
                 dp_size: int,
                 ep_size: int,
                 enable_caching: bool = True):
        self.num_experts = num_experts
        self.dp_size = dp_size
        self.ep_size = ep_size
        self.enable_caching = enable_caching
        
        # Load tracking
        self.load_tracker = ExpertLoadTracker(num_experts)
        
        # Expert routing cache for single token optimization
        self.routing_cache: Dict[int, Tuple[torch.Tensor, torch.Tensor]] = {}
        self.cache_hits = 0
        self.cache_misses = 0
        
        # Communication optimization
        self.last_communication_pattern: Optional[str] = None
        self.communication_stats: Dict[str, List[float]] = defaultdict(list)
        
        logger.info(f"SmartExpertScheduler initialized: "
                   f"experts={num_experts}, dp_size={dp_size}, ep_size={ep_size}")
    
    def should_eliminate_dummy_forwards(self, 
                                      batch_size: int,
                                      active_ranks: List[int]) -> Dict[int, bool]:
        """
        Determine which ranks can skip dummy forward passes.
        
        Returns:
            Dictionary mapping rank -> should_skip_dummy_forward
        """
        skip_decisions = {}
        
        # For single token decode, be aggressive about skipping dummy forwards
        if batch_size == 1:
            idle_ranks = self.load_tracker.get_idle_ranks(timeout_seconds=0.5)
            for rank in range(self.dp_size):
                if rank not in active_ranks and rank in idle_ranks:
                    skip_decisions[rank] = True
                else:
                    skip_decisions[rank] = False
        
        # For small batches, skip dummy forwards on mostly idle ranks
        elif batch_size <= 4:
            idle_ranks = self.load_tracker.get_idle_ranks(timeout_seconds=1.0)
            for rank in range(self.dp_size):
                if rank not in active_ranks and rank in idle_ranks:
                    # Skip if this rank has been consistently idle
                    skip_decisions[rank] = self.load_tracker.should_skip_dummy_forward(
                        rank, batch_size)
                else:
                    skip_decisions[rank] = False
        
        else:
            # For larger batches, use standard dummy forward behavior
            for rank in range(self.dp_size):
                skip_decisions[rank] = False
        
        return skip_decisions
    
    def optimize_communication_pattern(self, 
                                     batch_size: int,
                                     expert_distribution: Dict[int, List[int]]) -> str:
        """
        Choose optimal communication pattern based on batch size and expert distribution.
        
        Args:
            batch_size: Current batch size
            expert_distribution: Mapping of rank -> list of expert IDs
            
        Returns:
            Recommended communication pattern
        """
        if batch_size == 1:
            # For single token, use point-to-point if possible
            active_experts = set()
            for expert_list in expert_distribution.values():
                active_experts.update(expert_list)
            
            if len(active_experts) <= 2:
                return "point_to_point"
            else:
                return "async_all_to_all"
        
        elif batch_size <= 4:
            # For small batches, use async communication
            return "async_all_to_all"
        
        else:
            # For larger batches, use standard all-to-all
            return "standard_all_to_all"
    
    def get_cached_routing(self, input_hash: int) -> Optional[Tuple[torch.Tensor, torch.Tensor]]:
        """Get cached expert routing if available."""
        if not self.enable_caching:
            return None
        
        if input_hash in self.routing_cache:
            self.cache_hits += 1
            return self.routing_cache[input_hash]
        
        self.cache_misses += 1
        return None
    
    def cache_routing(self, input_hash: int, topk_weights: torch.Tensor, topk_ids: torch.Tensor):
        """Cache expert routing for future use."""
        if not self.enable_caching:
            return
        
        # Implement simple LRU cache
        if len(self.routing_cache) >= 1000:  # Max cache size
            # Remove oldest entry
            oldest_key = next(iter(self.routing_cache))
            del self.routing_cache[oldest_key]
        
        self.routing_cache[input_hash] = (topk_weights.clone(), topk_ids.clone())
    
    def update_rank_activity(self, rank: int, expert_ids: List[int], batch_size: int):
        """Update activity tracking for a rank."""
        self.load_tracker.update_expert_usage(rank, expert_ids, batch_size)
    
    def get_performance_stats(self) -> Dict[str, float]:
        """Get performance statistics."""
        total_cache_requests = self.cache_hits + self.cache_misses
        cache_hit_rate = self.cache_hits / max(1, total_cache_requests)
        
        return {
            'cache_hit_rate': cache_hit_rate,
            'cache_hits': self.cache_hits,
            'cache_misses': self.cache_misses,
            'load_balance_score': self.load_tracker.load_balance_score,
            'active_ranks': len(self.load_tracker.active_ranks),
            'total_ranks': self.dp_size
        }
    
    def reset_stats(self):
        """Reset performance statistics."""
        self.cache_hits = 0
        self.cache_misses = 0
        self.routing_cache.clear()
        self.load_tracker = ExpertLoadTracker(self.num_experts)


# Global scheduler instance
_smart_scheduler: Optional[SmartExpertScheduler] = None


def get_smart_expert_scheduler() -> Optional[SmartExpertScheduler]:
    """Get the global smart expert scheduler instance."""
    return _smart_scheduler


def initialize_smart_expert_scheduler(num_experts: int, 
                                    dp_size: int, 
                                    ep_size: int,
                                    **kwargs) -> None:
    """Initialize the global smart expert scheduler."""
    global _smart_scheduler
    _smart_scheduler = SmartExpertScheduler(
        num_experts=num_experts,
        dp_size=dp_size,
        ep_size=ep_size,
        **kwargs
    )


def should_skip_dummy_forward_for_rank(rank: int, batch_size: int) -> bool:
    """Check if dummy forward should be skipped for a specific rank."""
    scheduler = get_smart_expert_scheduler()
    if scheduler is None:
        return False
    
    return scheduler.load_tracker.should_skip_dummy_forward(rank, batch_size)
