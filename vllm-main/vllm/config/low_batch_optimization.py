# SPDX-License-Identifier: Apache-2.0
# SPDX-FileCopyrightText: Copyright contributors to the vLLM project

"""
Configuration for low batch size optimizations in vLLM.
Specifically designed for DeepSeek-R1 performance improvements.
"""

import os
from dataclasses import dataclass, field
from typing import Optional, Dict, Any, List
import logging

logger = logging.getLogger(__name__)


@dataclass
class LowBatchOptimizationConfig:
    """Configuration for low batch size optimizations."""
    
    # Enable/disable optimizations
    enable_low_batch_optimization: bool = True
    enable_single_token_optimization: bool = True
    enable_expert_caching: bool = True
    enable_memory_optimization: bool = True
    enable_smart_scheduling: bool = True
    
    # Batch size thresholds
    single_token_threshold: int = 1
    small_batch_threshold: int = 4
    microbatch_threshold: int = 8
    
    # Expert optimization settings
    expert_cache_size: int = 1024
    expert_prefetch_threshold: int = 2
    skip_eplb_for_small_batch: bool = True
    enable_expert_routing_cache: bool = True
    
    # Memory optimization settings
    memory_cache_size_mb: int = 64
    enable_weight_prefetching: bool = True
    optimize_tensor_layout: bool = True
    
    # Communication optimization settings
    use_async_communication: bool = True
    skip_dummy_forwards: bool = True
    dummy_forward_timeout_ms: float = 500.0
    min_active_ranks_for_sync: int = 2
    
    # CUDA graph optimization settings
    optimize_cudagraph_for_small_batch: bool = True
    minimal_cudagraph_padding: bool = True
    single_token_cudagraph_size: int = 1
    
    # Performance monitoring
    enable_performance_monitoring: bool = True
    log_optimization_stats: bool = False
    stats_logging_interval: int = 100
    
    # Model-specific settings
    deepseek_specific_optimizations: bool = True
    shared_expert_only_for_single_token: bool = False
    
    def __post_init__(self):
        """Validate configuration and apply environment variable overrides."""
        self._apply_env_overrides()
        self._validate_config()
        
        if self.enable_low_batch_optimization:
            logger.info("Low batch size optimizations enabled")
            if self.deepseek_specific_optimizations:
                logger.info("DeepSeek-specific optimizations enabled")
    
    def _apply_env_overrides(self):
        """Apply environment variable overrides."""
        env_mappings = {
            'VLLM_ENABLE_LOW_BATCH_OPT': 'enable_low_batch_optimization',
            'VLLM_ENABLE_SINGLE_TOKEN_OPT': 'enable_single_token_optimization',
            'VLLM_EXPERT_CACHE_SIZE': 'expert_cache_size',
            'VLLM_MEMORY_CACHE_SIZE_MB': 'memory_cache_size_mb',
            'VLLM_SKIP_DUMMY_FORWARDS': 'skip_dummy_forwards',
            'VLLM_DEEPSEEK_OPTIMIZATIONS': 'deepseek_specific_optimizations',
            'VLLM_SHARED_EXPERT_ONLY_SINGLE_TOKEN': 'shared_expert_only_for_single_token',
        }
        
        for env_var, config_attr in env_mappings.items():
            env_value = os.getenv(env_var)
            if env_value is not None:
                current_value = getattr(self, config_attr)
                if isinstance(current_value, bool):
                    setattr(self, config_attr, env_value.lower() in ('true', '1', 'yes'))
                elif isinstance(current_value, int):
                    setattr(self, config_attr, int(env_value))
                elif isinstance(current_value, float):
                    setattr(self, config_attr, float(env_value))
                else:
                    setattr(self, config_attr, env_value)
    
    def _validate_config(self):
        """Validate configuration values."""
        if self.expert_cache_size < 0:
            raise ValueError("expert_cache_size must be non-negative")
        
        if self.memory_cache_size_mb < 0:
            raise ValueError("memory_cache_size_mb must be non-negative")
        
        if self.single_token_threshold < 1:
            raise ValueError("single_token_threshold must be at least 1")
        
        if self.small_batch_threshold < self.single_token_threshold:
            raise ValueError("small_batch_threshold must be >= single_token_threshold")
        
        if self.dummy_forward_timeout_ms < 0:
            raise ValueError("dummy_forward_timeout_ms must be non-negative")
    
    def should_optimize_for_batch_size(self, batch_size: int) -> bool:
        """Check if optimizations should be applied for given batch size."""
        if not self.enable_low_batch_optimization:
            return False
        
        return batch_size <= self.small_batch_threshold
    
    def should_use_single_token_optimization(self, batch_size: int) -> bool:
        """Check if single token optimizations should be applied."""
        return (self.enable_single_token_optimization and 
                batch_size <= self.single_token_threshold)
    
    def should_skip_eplb(self, batch_size: int) -> bool:
        """Check if EPLB should be skipped for this batch size."""
        return (self.skip_eplb_for_small_batch and 
                batch_size <= self.small_batch_threshold)
    
    def should_use_expert_caching(self, batch_size: int) -> bool:
        """Check if expert caching should be used."""
        return (self.enable_expert_caching and 
                batch_size <= self.small_batch_threshold)
    
    def should_optimize_memory_access(self, batch_size: int) -> bool:
        """Check if memory access optimizations should be applied."""
        return (self.enable_memory_optimization and 
                batch_size <= self.small_batch_threshold)
    
    def should_use_smart_scheduling(self, batch_size: int) -> bool:
        """Check if smart scheduling should be used."""
        return (self.enable_smart_scheduling and 
                batch_size <= self.small_batch_threshold)
    
    def get_optimization_summary(self) -> Dict[str, Any]:
        """Get a summary of enabled optimizations."""
        return {
            'low_batch_optimization': self.enable_low_batch_optimization,
            'single_token_optimization': self.enable_single_token_optimization,
            'expert_caching': self.enable_expert_caching,
            'memory_optimization': self.enable_memory_optimization,
            'smart_scheduling': self.enable_smart_scheduling,
            'deepseek_optimizations': self.deepseek_specific_optimizations,
            'thresholds': {
                'single_token': self.single_token_threshold,
                'small_batch': self.small_batch_threshold,
                'microbatch': self.microbatch_threshold,
            },
            'cache_sizes': {
                'expert_cache': self.expert_cache_size,
                'memory_cache_mb': self.memory_cache_size_mb,
            }
        }


@dataclass
class MicrobatchingConfig:
    """Configuration for enhanced microbatching in DeepSeek models."""
    
    enable_adaptive_microbatching: bool = True
    min_microbatch_size: int = 1
    max_microbatch_size: int = 8
    target_utilization: float = 0.8
    
    # DeepSeek-specific microbatching
    deepseek_microbatch_strategy: str = "expert_aware"  # "expert_aware", "standard", "adaptive"
    expert_load_balancing: bool = True
    dynamic_batch_sizing: bool = True
    
    # Performance tuning
    microbatch_timeout_ms: float = 100.0
    batch_formation_delay_ms: float = 10.0
    
    def should_use_adaptive_microbatching(self, model_name: str) -> bool:
        """Check if adaptive microbatching should be used for this model."""
        return (self.enable_adaptive_microbatching and 
                "deepseek" in model_name.lower())
    
    def get_optimal_microbatch_size(self, 
                                  total_tokens: int,
                                  available_memory: int,
                                  expert_distribution: Optional[Dict[int, int]] = None) -> int:
        """Calculate optimal microbatch size based on current conditions."""
        if not self.enable_adaptive_microbatching:
            return min(total_tokens, self.max_microbatch_size)
        
        # Start with memory-based sizing
        memory_based_size = min(total_tokens, available_memory // 1024)  # Rough estimate
        
        # Apply expert distribution considerations for DeepSeek
        if (expert_distribution and 
            self.deepseek_microbatch_strategy == "expert_aware"):
            # Adjust based on expert load distribution
            max_expert_load = max(expert_distribution.values()) if expert_distribution else 1
            if max_expert_load > 10:  # High expert load
                memory_based_size = max(1, memory_based_size // 2)
        
        # Ensure within bounds
        return max(self.min_microbatch_size, 
                  min(memory_based_size, self.max_microbatch_size))


# Global configuration instances
_low_batch_config: Optional[LowBatchOptimizationConfig] = None
_microbatch_config: Optional[MicrobatchingConfig] = None


def get_low_batch_optimization_config() -> LowBatchOptimizationConfig:
    """Get the global low batch optimization configuration."""
    global _low_batch_config
    if _low_batch_config is None:
        _low_batch_config = LowBatchOptimizationConfig()
    return _low_batch_config


def get_microbatching_config() -> MicrobatchingConfig:
    """Get the global microbatching configuration."""
    global _microbatch_config
    if _microbatch_config is None:
        _microbatch_config = MicrobatchingConfig()
    return _microbatch_config


def initialize_low_batch_optimization_config(**kwargs) -> None:
    """Initialize the global low batch optimization configuration."""
    global _low_batch_config
    _low_batch_config = LowBatchOptimizationConfig(**kwargs)


def initialize_microbatching_config(**kwargs) -> None:
    """Initialize the global microbatching configuration."""
    global _microbatch_config
    _microbatch_config = MicrobatchingConfig(**kwargs)


def is_low_batch_optimization_enabled() -> bool:
    """Check if low batch optimization is enabled."""
    config = get_low_batch_optimization_config()
    return config.enable_low_batch_optimization


def should_apply_deepseek_optimizations(model_name: str) -> bool:
    """Check if DeepSeek-specific optimizations should be applied."""
    config = get_low_batch_optimization_config()
    return (config.deepseek_specific_optimizations and 
            "deepseek" in model_name.lower())


def log_optimization_stats(stats: Dict[str, Any]) -> None:
    """Log optimization statistics if enabled."""
    config = get_low_batch_optimization_config()
    if config.log_optimization_stats:
        logger.info(f"Low batch optimization stats: {stats}")
