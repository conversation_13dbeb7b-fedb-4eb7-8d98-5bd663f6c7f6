{%- if messages and messages[0]['role'] == 'system' %}
    {%- set system_message = messages[0]['content']|trim %}
    {%- set messages = messages[1:] %}
{%- else %}
    {%- set system_message = "You are a helpful assistant." %}
{%- endif %}

{%- if messages %}
<|system|>
{{ system_message }}
{%- if tools %}
In addition to plain text responses, you can chose to call one or more of the provided functions.

Use the following rule to decide when to call a function:
  * if the response can be generated from your internal knowledge (e.g., as in the case of queries like "What is the capital of Poland?"), do so
  * if you need external information that can be obtained by calling one or more of the provided functions, generate a function calls

If you decide to call functions:
  * prefix function calls with functools marker (no closing marker required)
  * all function calls should be generated in a single JSON list formatted as functools[{"name": [function name], "arguments": [function arguments as JSON]}, ...]
  * follow the provided JSON schema. Do not hallucinate arguments or values. Do to blindly copy values from the provided samples
  * respect the argument type formatting. E.g., if the type if number and format is float, write value 7 as 7.0
  * make sure you pick the right functions that match the user intent


        {%- for t in tools %}
            {{- t | tojson(indent=4) }}
            {{- "\n\n" }}
        {%- endfor %}
{%- endif %}<|end|>

    {%- for message in messages %}
        {%- if message.role != "system" %}
<|{{ message.role }}|>
            {%- if message.content and message.role == "tools" %}
{"result": {{ message.content }}}
            {%- elif message.content %}
{{ message.content }}
            {%- elif message.tool_calls %}
                {%- for call in message.tool_calls %}
{"name": "{{ call.function.name }}", "arguments": {{ call.function.arguments }}}
                    {%- if not loop.last %},{% endif %}
                {%- endfor %}
            {%- endif %}<|end|>
        {%- endif %}
    {%- endfor %}<|assistant|>

{%- else %}
    {%- if system_message %}
<|system|>

{{ system_message }}<|end|>
    {%- endif %}
    {%- if prompt %}
<|user|>

{{ prompt }}<|end|>
    {%- endif %}<|assistant|>

{%- endif %}
{{ response }}
{%- if response %}<|user|>{% endif %}