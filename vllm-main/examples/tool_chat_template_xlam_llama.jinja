{{- bos_token }}
{%- if custom_tools is defined %}
    {%- set tools = custom_tools %}
{%- endif %}
{%- if not tools_in_user_message is defined %}
    {%- set tools_in_user_message = true %}
{%- endif %}
{%- if not tools is defined %}
    {%- set tools = none %}
{%- endif %}

{#- Extract system message #}
{{- "<|start_header_id|>system<|end_header_id|>\n\n" }}
{%- if messages[0]['role'] == 'system' %}
    {%- set system_message = messages[0]['content'] | trim %}
    {%- set messages = messages[1:] %}
    {{- system_message + "\n" }}
{%- else %}
    {%- set system_message = "You are a helpful assistant. You are developed by Salesforce xLAM team." %}
    {% set format_instruction %}You have access to a set of tools. When using tools, make calls in a single JSON array: 

[{"name": "tool_call_name", "arguments": {"arg1": "value1", "arg2": "value2"}}, ... (additional parallel tool calls as needed)]

If no tool is suitable, state that explicitly. If the user's input lacks required parameters, ask for clarification. Do not interpret or respond until tool results are returned. Once they are available, process them or make additional calls if needed. For tasks that don't require tools, such as casual conversation or general advice, respond directly in plain text. The available tools are:{% endset %}
    {{- system_message + "\n" }}
    {%- if tools is not none %}
        {{- format_instruction + "\n\n" }}
    {%- endif %}
{%- endif %}


{%- if tools is not none %}
    {%- for t in tools %}
        {{- t | tojson(indent=4) }}
        {{- "\n\n" }}
    {%- endfor %}
{%- endif %}
{{- "<|eot_id|>" }}

{%- for message in messages %}
    {%- if not (message.role == 'ipython' or message.role == 'tool' or 'tool_calls' in message) %}
        {{- '<|start_header_id|>' + message['role'] + '<|end_header_id|>\n\n'+ message['content'] | trim + '<|eot_id|>' }}
    {%- elif 'tool_calls' in message %}
        {{- '<|start_header_id|>assistant<|end_header_id|>\n\n' -}}
        {%- if message['tool_calls'] %}
            {{- "[" }}
            {%- for tool_call_function in message.tool_calls %}
                {%- set tool_call = tool_call_function.function %}
                {{- '{"name": "' + tool_call.name + '", ' }}
                {{- '"arguments": ' }}
                {{- tool_call.arguments | tojson }}
                {{- "}" }}
                {%- if not loop.last %}
                    {{- ", " }}
                {%- endif %}
            {%- endfor %}
            {{- "]" }}
            {{- "<|eot_id|>" }}
        {%- elif message['content'] %}
            {{- message['content'] | trim + '<|eot_id|>' }}
        {%- else %}
            {{- "[]\n" + '<|eot_id|>' }}
        {%- endif %}
    {%- elif message.role == "tool" or message.role == "ipython" %}
        {{- "<|start_header_id|>" + "ipython" + "<|end_header_id|>\n\n" }}
        {%- set content = message["content"] %}
        {%- if content is mapping or (content is iterable and content is not string) %}
            {{- content | tojson }}
        {%- else %}
            {{- content }}
        {%- endif %}
        {{- "<|eot_id|>" }}
    {%- endif %}
{%- endfor %}
{%- if add_generation_prompt %}
    {{- '<|start_header_id|>assistant<|end_header_id|>\n\n' }}
{%- endif %}