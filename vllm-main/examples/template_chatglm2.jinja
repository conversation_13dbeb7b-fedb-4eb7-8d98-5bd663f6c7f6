{%- set counter = namespace(index=1) -%}
{%- for message in messages -%}
    {%- if message['role'] == 'user' -%}
        {{- '[Round ' + counter.index|string + ']\n\n问：' + message['content'] -}}
        {%- set counter.index = counter.index + 1 -%}
    {%- endif -%}
    {%- if message['role'] == 'assistant' -%}
        {{- '\n\n答：' + message['content'] -}}
        {%- if (loop.last and add_generation_prompt) or not loop.last -%}
            {{- '\n\n' -}}
        {%- endif -%}
    {%- endif -%}
{%- endfor -%}


{%- if add_generation_prompt and messages[-1]['role'] != 'assistant' -%}
    {{- '\n\n答：' -}}
{%- endif -%}